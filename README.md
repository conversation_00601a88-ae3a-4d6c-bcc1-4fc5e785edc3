# OA数据爬虫程序

这是一个用于抓取汕头大学OA系统数据的爬虫程序。

## 功能特点

- 自动分页抓取数据
- 目标抓取1万条记录
- 支持CSV和JSON格式输出
- 包含错误处理和重试机制
- 自动备份功能（每100页备份一次）
- 详细的日志记录
- 支持中断恢复

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python oa_scraper.py
```

### 程序说明

程序会自动：
1. 从第1页开始抓取数据
2. 每页抓取10条记录
3. 自动处理分页逻辑
4. 保存数据到`oa_data.csv`和`oa_data.json`
5. 每100页自动备份数据
6. 记录详细日志到`scraper.log`

### 输出文件

- `oa_data.csv` - 最终的CSV格式数据
- `oa_data.json` - 最终的JSON格式数据
- `oa_data_backup_*.csv` - 每100页的备份文件
- `oa_data_backup_*.json` - 每100页的备份文件
- `scraper.log` - 详细的运行日志

### 中断处理

如果程序被中断（Ctrl+C）或发生错误，会自动保存已抓取的数据到：
- `oa_data_interrupted.csv` - 中断时的数据
- `oa_data_interrupted.json` - 中断时的数据
- `oa_data_error.csv` - 错误时的数据
- `oa_data_error.json` - 错误时的数据

## 配置说明

可以在`oa_scraper.py`中修改以下参数：

- `total_target` - 目标抓取记录数（默认10000）
- `records_per_page` - 每页记录数（默认10）
- `delay` - 请求间隔秒数（默认1秒）

## 注意事项

1. 请遵守网站的robots.txt和使用条款
2. 适当设置请求间隔，避免对服务器造成过大压力
3. 程序会自动处理网络错误和重试
4. 建议在网络稳定的环境下运行
5. 抓取大量数据需要较长时间，请耐心等待

## 数据格式

每条记录包含以下字段：
- `page` - 页码
- `content` - 提取的文本内容
- `html` - 原始HTML内容
- `timestamp` - 抓取时间戳

## 故障排除

1. **网络连接问题**：检查网络连接，程序会自动重试
2. **权限问题**：确保Token有效且未过期
3. **解析问题**：如果数据格式发生变化，可能需要调整解析逻辑

## 技术栈

- Python 3.6+
- requests - HTTP请求库
- BeautifulSoup4 - HTML解析库
- lxml - XML/HTML解析器
