#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA数据爬虫程序
抓取http://wechat.stu.edu.cn/oa/OA_list.html的数据
目标：抓取1万条记录
"""

import requests
import csv
import time
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OAScraper:
    def __init__(self):
        self.base_url = "http://wechat.stu.edu.cn/oa/OA_list.html"
        self.token = "94cw0IjFHPvqCb/OHlgtJT3yU09jZw1zvb7R/nQhTwA="
        self.session = requests.Session()
        
        # 设置请求头模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.records = []
        self.total_target = 10000  # 目标抓取1万条记录
        self.records_per_page = 10  # 每页10条记录
        self.delay = 1  # 请求间隔（秒）
        
    def get_page_data(self, page_no):
        """获取指定页面的数据"""
        params = {
            'TokenOa': self.token,
            'PageContainsRecord': self.records_per_page,
            'CurrentPageNo': page_no
        }
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            logging.info(f"成功获取第{page_no}页数据，状态码：{response.status_code}")
            return response.text
            
        except requests.exceptions.RequestException as e:
            logging.error(f"获取第{page_no}页数据失败：{e}")
            return None
    
    def parse_page_data(self, html_content, page_no):
        """解析页面数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 这里需要根据实际的HTML结构来解析数据
            # 由于我无法看到实际的页面结构，这里提供一个通用的解析框架
            
            page_records = []
            
            # 尝试找到数据表格或列表
            # 常见的选择器：table, .list, .item, .record等
            data_containers = soup.find_all(['table', 'div', 'ul', 'ol'])
            
            for container in data_containers:
                # 尝试提取文本内容
                rows = container.find_all(['tr', 'li', 'div'])
                for row in rows:
                    text_content = row.get_text(strip=True)
                    if text_content and len(text_content) > 10:  # 过滤掉太短的内容
                        record = {
                            'page': page_no,
                            'content': text_content,
                            'html': str(row),
                            'timestamp': datetime.now().isoformat()
                        }
                        page_records.append(record)
            
            # 如果没有找到结构化数据，保存整个页面内容
            if not page_records:
                record = {
                    'page': page_no,
                    'content': soup.get_text(strip=True),
                    'html': html_content,
                    'timestamp': datetime.now().isoformat()
                }
                page_records.append(record)
            
            logging.info(f"第{page_no}页解析到{len(page_records)}条记录")
            return page_records
            
        except Exception as e:
            logging.error(f"解析第{page_no}页数据失败：{e}")
            return []
    
    def save_to_csv(self, filename='oa_data.csv'):
        """保存数据到CSV文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['page', 'content', 'html', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for record in self.records:
                    writer.writerow(record)
            
            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")
            
        except Exception as e:
            logging.error(f"保存CSV文件失败：{e}")
    
    def save_to_json(self, filename='oa_data.json'):
        """保存数据到JSON文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.records, jsonfile, ensure_ascii=False, indent=2)
            
            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")
            
        except Exception as e:
            logging.error(f"保存JSON文件失败：{e}")
    
    def scrape(self):
        """主要的爬取方法"""
        logging.info(f"开始爬取数据，目标：{self.total_target}条记录")
        
        page_no = 1
        consecutive_failures = 0
        max_consecutive_failures = 5
        
        while len(self.records) < self.total_target:
            logging.info(f"正在爬取第{page_no}页...")
            
            # 获取页面数据
            html_content = self.get_page_data(page_no)
            
            if html_content is None:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logging.error(f"连续{max_consecutive_failures}次失败，停止爬取")
                    break
                
                logging.warning(f"第{page_no}页获取失败，等待{self.delay * 2}秒后重试...")
                time.sleep(self.delay * 2)
                continue
            
            # 重置失败计数
            consecutive_failures = 0
            
            # 解析页面数据
            page_records = self.parse_page_data(html_content, page_no)
            
            if not page_records:
                logging.warning(f"第{page_no}页没有解析到数据，可能已到达最后一页")
                break
            
            # 添加到总记录中
            self.records.extend(page_records)
            
            logging.info(f"已爬取{len(self.records)}条记录，进度：{len(self.records)/self.total_target*100:.1f}%")
            
            # 每100页保存一次数据（防止数据丢失）
            if page_no % 100 == 0:
                self.save_to_csv(f'oa_data_backup_{page_no}.csv')
                self.save_to_json(f'oa_data_backup_{page_no}.json')
            
            page_no += 1
            
            # 延时避免被封
            time.sleep(self.delay)
        
        # 最终保存
        self.save_to_csv()
        self.save_to_json()
        
        logging.info(f"爬取完成！总共获取{len(self.records)}条记录")

def main():
    """主函数"""
    scraper = OAScraper()
    
    try:
        scraper.scrape()
    except KeyboardInterrupt:
        logging.info("用户中断爬取")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_interrupted.csv')
        scraper.save_to_json('oa_data_interrupted.json')
    except Exception as e:
        logging.error(f"爬取过程中发生错误：{e}")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_error.csv')
        scraper.save_to_json('oa_data_error.json')

if __name__ == "__main__":
    main()
